<template>
    <div class="ai-agent">
        <div class="ai-agent__switch">
            <el-switch
                v-model="enableAgent"
                :disabled="loading"
                @change="handleSwitchChange"
            ></el-switch>
            <span>{{ $t('aiAgent.title') }}</span>
        </div>
        <div class="ai-agent__description">
            <p>{{ $t('aiAgent.description1') }}</p>
            <p>{{ $t('aiAgent.description2') }}</p>
            <p>{{ $t('aiAgent.description3') }}</p>
        </div>
        <template v-if="enableAgent">
            <div class="ai-agent__config" v-loading="loading">
                <h3>{{ $t('aiAgent.configTitle') }}</h3>
                <p class="config-tip">{{ $t('aiAgent.configTip') }}</p>

                <draggable
                    v-model="ruleList"
                    :disabled="!enableAgent"
                    handle=".drag-handle"
                    ghost-class="ghost"
                    @end="onDragEnd"
                >
                    <div
                        v-for="(rule, index) in ruleList"
                        :key="rule.strategy"
                        class="rule-item"
                        :class="{ 'disabled': !enableAgent }"
                    >
                        <div class="rule-header">
                            <div class="rule-content">
                                <i class="drag-handle el-icon-rank" :class="{ 'disabled': !enableAgent }"></i>
                                <span class="rule-order">{{ index + 1 }}.</span>
                                <el-checkbox
                                    v-model="rule.isOpen"
                                    :disabled="!enableAgent"
                                    @change="updateRuleEnabled(rule)"
                                >
                                    {{ rule.label }}
                                </el-checkbox>
                            </div>
                        </div>
                        <p class="rule-desc">{{ rule.description }}</p>
                    </div>
                </draggable>
            </div>

            <div class="ai-agent__adjustment">
                <h3>{{ $t('aiAgent.adjustmentTitle') }}</h3>
                <div class="adjustment-item">
                    <p>{{ $t('aiAgent.positionAdjustment') }}</p>
                    <div class="position-adjust">
                        <div class="adjust-row">
                            <span>{{ $t('aiAgent.moveUp') }}</span>
                            <el-input v-model="sealMoveConfig.upMove" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                            <span>{{ $t('aiAgent.centimeter') }}</span>
                        </div>
                        <div class="adjust-row">
                            <span>{{ $t('aiAgent.moveLeft') }}</span>
                            <el-input v-model="sealMoveConfig.leftMove" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                            <span>{{ $t('aiAgent.centimeter') }}</span>
                        </div>
                    </div>
                    <p class="adjust-tip">{{ $t('aiAgent.adjustTip1') }}</p>
                    <p class="adjust-tip">{{ $t('aiAgent.adjustTip2') }}</p>
                    <p class="adjust-tip">{{ $t('aiAgent.adjustTip3') }}</p>
                </div>

                <div class="adjustment-item">
                    <p>{{ $t('aiAgent.contentAdjustment') }}</p>
                    <div class="content-adjust">
                        <div class="adjust-row">
                            <span>{{ $t('aiAgent.addSealPrefix') }}</span>
                            <el-input v-model="addSealByKeyword.keyword" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                            <span>{{ $t('aiAgent.addSealSuffix') }}</span>
                        </div>
                        <div class="adjust-row">
                            <span>{{ $t('aiAgent.removeSealPrefix') }}</span>
                            <el-input v-model="removeSealByKeyword.keyword" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                            <span>{{ $t('aiAgent.removeSealSuffix') }}</span>
                        </div>
                    </div>
                    <p class="adjust-tip">{{ $t('aiAgent.adjustTip4') }}</p>
                </div>
            </div>

            <div class="ai-agent__actions">
                <el-button
                    type="primary"
                    :loading="loading"
                    :disabled="!enableAgent"
                    @click="saveConfig"
                >
                    {{ $t('aiAgent.saveConfig') }}
                </el-button>
                <el-button @click="resetConfig">{{ $t('aiAgent.resetConfig') }}</el-button>
            </div>
        </template>
    </div>
</template>

<script>
import { getStampRecommendationRule, saveStampRecommendationRule, toggleStampRecommendationSwitch } from 'src/api/template/index.js';
import { mapState } from 'vuex';
import draggable from 'vuedraggable';

export default {
    name: 'AIAgent',
    components: {
        draggable,
    },
    props: {
        templateId: {
            type: String,
            required: true,
        },
        templateName: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            enableAgent: false,
            loading: false,
            // 规则列表，支持排序
            ruleList: [],
            // 位置调优配置
            sealMoveConfig: {
                enabled: false,
                upMove: '0',
                leftMove: '0',
            },
            // 关键字配置
            addSealByKeyword: {
                enabled: false,
                keyword: '',
            },
            removeSealByKeyword: {
                enabled: false,
                keyword: '',
            },
        };
    },
    computed: {
        ...mapState([
            'features',
        ]),
        // 检查拖章功能是否可用
        isStampRecommendationAvailable() {
            return this.features.includes('244');
        },
    },
    watch: {
        // 监听位置调优输入，自动设置enabled状态
        'sealMoveConfig.upMove'() {
            this.updateMoveConfigEnabled();
        },
        'sealMoveConfig.leftMove'() {
            this.updateMoveConfigEnabled();
        },
        // 监听关键字输入，自动设置enabled状态
        'addSealByKeyword.keyword'() {
            this.addSealByKeyword.enabled = !!this.addSealByKeyword.keyword.trim();
        },
        'removeSealByKeyword.keyword'() {
            this.removeSealByKeyword.enabled = !!this.removeSealByKeyword.keyword.trim();
        },
    },
    methods: {
        // 拖拽结束事件
        onDragEnd() {
            // 更新排序
            this.ruleList.forEach((rule, index) => {
                rule.order = index + 1;
            });
        },

        // 初始化规则列表
        initRuleList() {
            this.ruleList = [
                {
                    strategy: 'NEED_OVERLAPPING_SEAL',
                    label: this.$t('aiAgent.rule.needOverlappingSeal'),
                    description: this.$t('aiAgent.rule.needOverlappingSealDesc'),
                    isOpen: false,
                    order: 1,
                },
                {
                    strategy: 'RECIPROCAL_SEALING',
                    label: this.$t('aiAgent.rule.reciprocalSealing'),
                    description: this.$t('aiAgent.rule.reciprocalSealingDesc'),
                    isOpen: false,
                    order: 2,
                },
                {
                    strategy: 'SEAL_ON_LAST_LINE_OF_TEXT',
                    label: this.$t('aiAgent.rule.sealOnLastLine'),
                    description: this.$t('aiAgent.rule.sealOnLastLineDesc'),
                    isOpen: true, // 默认勾选
                    order: 3,
                },
                {
                    strategy: 'REASONABLE_AREA_SEALING',
                    label: this.$t('aiAgent.rule.reasonableAreaSealing'),
                    description: this.$t('aiAgent.rule.reasonableAreaSealingDesc'),
                    isOpen: true, // 默认勾选
                    order: 4,
                },
                // 暂时注释掉每页盖章规则
                // {
                //     strategy: 'SEAL_EACH_PAGE',
                //     label: this.$t('aiAgent.rule.sealEachPage'),
                //     description: this.$t('aiAgent.rule.sealEachPageDesc'),
                //     isOpen: false,
                //     order: 5,
                // },
            ];
        },

        // 更新规则启用状态
        updateRuleEnabled(rule) {
            // 可以在这里添加额外的逻辑
            console.log(`规则 ${rule.strategy} 状态变更为: ${rule.isOpen}`);
        },

        // 加载配置
        async loadConfig() {
            if (!this.templateId) {
                return;
            }

            this.loading = true;
            try {
                const response = await getStampRecommendationRule(this.templateId);
                const data = response.data;

                // 检查功能是否可用，如果不可用则强制关闭
                if (!this.isStampRecommendationAvailable) {
                    this.enableAgent = false;
                } else {
                    // 设置开关状态
                    this.enableAgent = data.useStampRecommendation || false;
                }

                // 解析策略配置
                if (data.stampStrategies && Array.isArray(data.stampStrategies)) {
                    this.parseStrategiesFromAPI(data.stampStrategies);
                }
            } catch (error) {
                console.error('加载印章推荐配置失败:', error);
                this.$message.error(this.$t('aiAgent.loadFailed'));
            } finally {
                this.loading = false;
            }
        },

        // 解析API返回的策略配置
        parseStrategiesFromAPI(strategies) {
            // 重置所有配置
            this.resetConfig();

            // 创建策略映射，按order排序
            const strategyMap = {};
            strategies.forEach(strategy => {
                strategyMap[strategy.strategy] = strategy;
            });

            // 更新规则列表
            this.ruleList.forEach(rule => {
                const apiStrategy = strategyMap[rule.strategy];
                if (apiStrategy) {
                    const isOpen = apiStrategy.isOpen;
                    rule.isOpen = isOpen !== undefined ? isOpen : true;
                    rule.order = apiStrategy.order || rule.order;
                }
            });

            // 按order排序
            this.ruleList.sort((a, b) => {
                const aOrder = strategyMap[a.strategy]?.order || a.order;
                const bOrder = strategyMap[b.strategy]?.order || b.order;
                return aOrder - bOrder;
            });

            // 处理特殊配置
            strategies.forEach(strategy => {
                const { strategy: strategyType, strategyConfigParam, isOpen } = strategy;

                // 解析JSON字符串参数
                let configParam = {};
                if (strategyConfigParam && typeof strategyConfigParam === 'string') {
                    try {
                        configParam = JSON.parse(strategyConfigParam);
                    } catch (error) {
                        console.warn('解析strategyConfigParam失败:', error);
                        configParam = {};
                    }
                } else if (strategyConfigParam && typeof strategyConfigParam === 'object') {
                    configParam = strategyConfigParam;
                }

                switch (strategyType) {
                    case 'SEAL_MOVE_CONFIG':
                        this.sealMoveConfig.enabled = isOpen !== undefined ? isOpen : true;
                        this.sealMoveConfig.upMove = configParam.upMove || '0';
                        this.sealMoveConfig.leftMove = configParam.leftMove || '0';
                        break;
                    case 'ADD_SEAL_BY_KEYWORD':
                        this.addSealByKeyword.enabled = isOpen !== undefined ? isOpen : true;
                        this.addSealByKeyword.keyword = configParam.keyword || '';
                        break;
                    case 'REMOVE_SEAL_BY_KEYWORD':
                        this.removeSealByKeyword.enabled = isOpen !== undefined ? isOpen : true;
                        this.removeSealByKeyword.keyword = configParam.keyword || '';
                        break;
                }
            });
        },

        // 重置配置
        resetConfig() {
            // 重置规则列表，保持默认勾选项
            this.ruleList.forEach(rule => {
                // 只有默认勾选的项保持勾选状态
                if (rule.strategy === 'SEAL_ON_LAST_LINE_OF_TEXT' ||
                    rule.strategy === 'REASONABLE_AREA_SEALING') {
                    rule.isOpen = true;
                } else {
                    rule.isOpen = false;
                }
            });

            // 重置特殊配置
            this.sealMoveConfig = {
                enabled: false,
                upMove: '0',
                leftMove: '0',
            };
            this.addSealByKeyword = {
                enabled: false,
                keyword: '',
            };
            this.removeSealByKeyword = {
                enabled: false,
                keyword: '',
            };
        },

        // 保存配置
        async saveConfig() {
            if (!this.templateId) {
                this.$message.error(this.$t('aiAgent.templateIdRequired'));
                return;
            }

            this.loading = true;
            try {
                const ruleConfig = this.buildRuleConfig();
                await saveStampRecommendationRule(this.templateId, ruleConfig);
                this.$message.success(this.$t('aiAgent.saveSuccess'));
            } catch (error) {
                console.error('保存印章推荐配置失败:', error);
                this.$message.error(this.$t('aiAgent.saveFailed'));
            } finally {
                this.loading = false;
            }
        },

        // 构建规则配置对象
        buildRuleConfig() {
            const stampStrategies = [];
            let currentOrder = 1;

            // 添加所有规则策略，按排序顺序（包括未启用的）
            this.ruleList.forEach((rule, index) => {
                stampStrategies.push({
                    order: index + 1,
                    strategy: rule.strategy,
                    strategyConfigParam: '{}',
                    isOpen: rule.isOpen,
                });
            });

            // 更新当前排序位置
            currentOrder = this.ruleList.length + 1;

            // 添加位置调优配置
            if (this.sealMoveConfig.enabled) {
                const upMove = parseFloat(this.sealMoveConfig.upMove) || 0;
                const leftMove = parseFloat(this.sealMoveConfig.leftMove) || 0;
                if (upMove !== 0 || leftMove !== 0) {
                    stampStrategies.push({
                        order: currentOrder++,
                        strategy: 'SEAL_MOVE_CONFIG',
                        strategyConfigParam: JSON.stringify({
                            upMove: upMove.toString(),
                            leftMove: leftMove.toString(),
                        }),
                        isOpen: true,
                    });
                }
            }

            // 添加关键字配置
            if (this.addSealByKeyword.enabled && this.addSealByKeyword.keyword.trim()) {
                stampStrategies.push({
                    order: currentOrder++,
                    strategy: 'ADD_SEAL_BY_KEYWORD',
                    strategyConfigParam: JSON.stringify({
                        keyword: this.addSealByKeyword.keyword.trim(),
                    }),
                    isOpen: true,
                });
            }

            if (this.removeSealByKeyword.enabled && this.removeSealByKeyword.keyword.trim()) {
                stampStrategies.push({
                    order: currentOrder++,
                    strategy: 'REMOVE_SEAL_BY_KEYWORD',
                    strategyConfigParam: JSON.stringify({
                        keyword: this.removeSealByKeyword.keyword.trim(),
                    }),
                    isOpen: true,
                });
            }

            return {
                useStampRecommendation: this.enableAgent,
                stampStrategies,
            };
        },

        // 更新位置调优enabled状态
        updateMoveConfigEnabled() {
            const upMove = parseFloat(this.sealMoveConfig.upMove) || 0;
            const leftMove = parseFloat(this.sealMoveConfig.leftMove) || 0;
            this.sealMoveConfig.enabled = upMove !== 0 || leftMove !== 0;
        },

        // 处理开关切换
        async handleSwitchChange(value) {
            // 检查功能是否可用
            if (value && !this.isStampRecommendationAvailable) {
                this.$message.error(this.$t('aiAgent.featureNotAvailable'));
                this.enableAgent = false; // 强制关闭
                return;
            }

            this.loading = true;
            try {
                await toggleStampRecommendationSwitch(this.templateId, value);
                if (value) {
                    this.saveConfig();
                } else {
                    this.$message.success(this.$t('aiAgent.disableSuccess'));
                }
            } catch (error) {
                console.error('切换印章推荐开关失败:', error);
                this.enableAgent = !value; // 回滚状态
            } finally {
                this.loading = false;
            }
        },
    },
    mounted() {
        this.initRuleList();
        this.loadConfig();
    },
};
</script>

<style lang="scss">
.ai-agent {
    padding: 20px;

    &__switch {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        span {
            margin-left: 10px;
            font-size: 14px;
        }

        .feature-tip {
            color: #999;
            font-size: 12px;
        }
    }

    &__description {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;

        p {
            margin: 5px 0;
            font-size: 13px;
            line-height: 1.5;
        }
    }

    &__config {
        margin-bottom: 30px;

        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .config-tip {
            font-size: 13px;
            color: #666;
            margin-bottom: 15px;
        }
    }

    .rule-item {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #fff;
        transition: all 0.3s ease;

        &:hover {
            border-color: #ddd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.disabled {
            background-color: #f5f5f5;
            opacity: 0.6;
        }

        .rule-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rule-content {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .drag-handle {
            cursor: move;
            margin-right: 8px;
            color: #999;
            font-size: 16px;

            &.disabled {
                cursor: not-allowed;
                color: #ccc;
            }

            &:hover:not(.disabled) {
                color: #666;
            }
        }

        .rule-order {
            margin-right: 8px;
            font-weight: bold;
            color: #666;
            min-width: 20px;
        }

        .rule-desc {
            margin-top: 8px;
            margin-left: 32px;
            font-size: 12px;
            color: #999;
            line-height: 1.4;
        }
    }

    .ghost {
        opacity: 0.5;
        background-color: #f0f0f0;
    }

    &__adjustment {
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .adjustment-item {
            margin-bottom: 20px;

            p {
                margin-bottom: 10px;
            }

            .position-adjust, .content-adjust {
                margin-left: 20px;
                margin-bottom: 10px;
            }

            .adjust-row {
                display: flex;
                align-items: center;
                margin-bottom: 10px;

                span {
                    font-size: 13px;
                }
            }

            .adjust-input {
                width: 80px;
                margin: 0 10px;
            }

            .content-input {
                width: 300px;
                margin: 0 10px;
            }

            .adjust-tip {
                font-size: 12px;
                color: #999;
                margin-left: 20px;
            }
        }
    }

    &__actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;

        .el-button {
            margin: 0 10px;
        }
    }
}
</style>
